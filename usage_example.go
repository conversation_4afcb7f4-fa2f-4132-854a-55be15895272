package main

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/sashabaranov/go-openai"
)

// 演示如何处理包含 cached_tokens 的 Usage 信息
func demonstrateUsageHandling() {
	fmt.Println("=== 演示 Usage 信息处理 ===")

	// 模拟从 go-openai 包接收到的 Usage 信息（类似于你提到的模型返回）
	openaiUsage := openai.Usage{
		PromptTokens:     754,
		CompletionTokens: 44,
		TotalTokens:      798,
		PromptTokensDetails: &openai.PromptTokensDetails{
			AudioTokens:  0,
			CachedTokens: 3, // 这是关键的 cached_tokens 字段
		},
		CompletionTokensDetails: &openai.CompletionTokensDetails{
			AudioTokens:     0,
			ReasoningTokens: 0,
		},
	}

	fmt.Printf("原始 openai.Usage: %+v\n", openaiUsage)

	// 使用我们修改后的代码逻辑来处理 Usage 信息
	usage := &Usage{
		CompletionTokens: openaiUsage.CompletionTokens,
		PromptTokens:     openaiUsage.PromptTokens,
		TotalTokens:      openaiUsage.TotalTokens,
	}

	// 处理 CompletionTokensDetails
	if openaiUsage.CompletionTokensDetails != nil {
		usage.CompletionTokensDetails = make(map[string]interface{})
		if openaiUsage.CompletionTokensDetails.AudioTokens > 0 {
			usage.CompletionTokensDetails["audio_tokens"] = openaiUsage.CompletionTokensDetails.AudioTokens
		}
		if openaiUsage.CompletionTokensDetails.ReasoningTokens > 0 {
			usage.CompletionTokensDetails["reasoning_tokens"] = openaiUsage.CompletionTokensDetails.ReasoningTokens
		}
	}

	// 处理 PromptTokensDetails，包括 cached_tokens
	if openaiUsage.PromptTokensDetails != nil {
		usage.PromptTokensDetails = make(map[string]interface{})
		if openaiUsage.PromptTokensDetails.AudioTokens > 0 {
			usage.PromptTokensDetails["audio_tokens"] = openaiUsage.PromptTokensDetails.AudioTokens
		}
		if openaiUsage.PromptTokensDetails.CachedTokens > 0 {
			usage.PromptTokensDetails["cached_tokens"] = openaiUsage.PromptTokensDetails.CachedTokens
		}
	}

	fmt.Printf("转换后的 Usage: %+v\n", usage)

	// 创建完整的 ChatStreamResponse
	response := ChatStreamResponse{
		ID:      "chatcmpl-openai-1753347138-1753347138",
		Object:  "chat.completion.chunk",
		Created: time.Now().Unix(),
		Model:   "Kimi-K2-Instruct",
		Choices: []ChatStreamChoice{
			{
				Index: 0,
				Delta: ChatStreamDelta{},
			},
		},
		Usage: usage,
	}

	// 序列化为 JSON
	jsonData, err := json.MarshalIndent(response, "", "  ")
	if err != nil {
		fmt.Printf("JSON 序列化失败: %v\n", err)
		return
	}

	fmt.Println("\n=== 最终的 JSON 输出 ===")
	fmt.Println(string(jsonData))

	fmt.Println("\n=== 对比 ===")
	fmt.Println("你期望的格式:")
	expectedJSON := `{
  "object": "chat.completion.chunk",
  "id": "0b4358a217533471593603694e2973",
  "choices": [],
  "created": 1753347165,
  "model": "Kimi-K2-Instruct",
  "usage": {
    "completion_tokens": 44,
    "prompt_tokens": 754,
    "total_tokens": 798,
    "completion_tokens_details": {},
    "prompt_tokens_details": {
      "cached_tokens": 3
    }
  }
}`
	fmt.Println(expectedJSON)

	fmt.Println("\n我们的输出:")
	fmt.Println(string(jsonData))

	// 验证 cached_tokens 是否正确包含
	var result map[string]interface{}
	err = json.Unmarshal(jsonData, &result)
	if err != nil {
		fmt.Printf("JSON 解析失败: %v\n", err)
		return
	}

	if usageMap, ok := result["usage"].(map[string]interface{}); ok {
		if promptDetails, ok := usageMap["prompt_tokens_details"].(map[string]interface{}); ok {
			if cachedTokens, exists := promptDetails["cached_tokens"]; exists {
				fmt.Printf("\n✅ 成功！cached_tokens 值为: %v\n", cachedTokens)
			} else {
				fmt.Println("\n❌ 错误！cached_tokens 字段不存在")
			}
		} else {
			fmt.Println("\n❌ 错误！prompt_tokens_details 字段不存在或格式错误")
		}
	} else {
		fmt.Println("\n❌ 错误！usage 字段不存在或格式错误")
	}
}

func main() {
	demonstrateUsageHandling()
}
