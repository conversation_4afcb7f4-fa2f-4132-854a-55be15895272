package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"codefuse-cli/utils"
	"github.com/sashabaranov/go-openai"
)

// ChatRequest 请求结构体 - 支持自定义格式
type ChatRequest struct {
	Query     string `json:"query"`
	TaskID    string `json:"taskId"`
	ModelName string `json:"modelName"`
}

// OpenAIChatRequest OpenAI兼容的请求结构体
type OpenAIChatRequest struct {
	Model       string                         `json:"model,omitempty"`
	Messages    []openai.ChatCompletionMessage `json:"messages"`
	Stream      bool                           `json:"stream,omitempty"`
	Temperature float32                        `json:"temperature,omitempty"`
	MaxTokens   int                            `json:"max_tokens,omitempty"`
}

// ChatStreamResponse OpenAI兼容的流式响应结构体
type ChatStreamResponse struct {
	ID      string             `json:"id"`
	Object  string             `json:"object"`
	Created int64              `json:"created"`
	Model   string             `json:"model"`
	Choices []ChatStreamChoice `json:"choices"`
	Usage   *Usage             `json:"usage,omitempty"`
}

type ChatStreamChoice struct {
	Index  int             `json:"index"`
	Delta  ChatStreamDelta `json:"delta"`
	Finish string          `json:"finish_reason,omitempty"`
}

type ChatStreamDelta struct {
	Role      string     `json:"role,omitempty"`
	Content   string     `json:"content,omitempty"`
	ToolCalls []ToolCall `json:"tool_calls,omitempty"`
}

type ToolCall struct {
	Index    *int         `json:"index,omitempty"`
	ID       string       `json:"id,omitempty"`
	Type     string       `json:"type,omitempty"`
	Function ToolFunction `json:"function,omitempty"`
}

type ToolFunction struct {
	Name      string `json:"name,omitempty"`
	Arguments string `json:"arguments,omitempty"`
}

// Usage 表示token使用情况
type Usage struct {
	CompletionTokens        int                    `json:"completion_tokens"`
	PromptTokens            int                    `json:"prompt_tokens"`
	TotalTokens             int                    `json:"total_tokens"`
	CompletionTokensDetails map[string]interface{} `json:"completion_tokens_details,omitempty"`
	PromptTokensDetails     map[string]interface{} `json:"prompt_tokens_details,omitempty"`
}

// HTTPChatService HTTP模式的聊天服务
type HTTPChatService struct {
	*ChatService
}

// NewHTTPChatService 创建HTTP聊天服务
func NewHTTPChatService(config *utils.Config, workspaceRoot string) *HTTPChatService {
	return &HTTPChatService{
		ChatService: NewChatService(config, workspaceRoot),
	}
}

// refreshGitInfo 刷新git信息，确保获取最新的仓库状态
func (hcs *HTTPChatService) refreshGitInfo() {
	// 调用ChatService的RefreshGitInfo方法，统一刷新所有相关组件
	err := hcs.ChatService.RefreshGitInfo()
	if err != nil {
		log.Printf("Warning: Failed to refresh git info and components: %v", err)
	}
}

// runHTTPServer 启动HTTP服务器
func runHTTPServer(port string) {
	// 获取当前工作目录作为工作空间根目录
	workspaceRoot, err := os.Getwd()
	if err != nil {
		fmt.Printf("Failed to get current directory: %v\n", err)
		os.Exit(1)
	}

	// 加载配置
	config, err := utils.LoadConfig()
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	chatService := NewHTTPChatService(config, workspaceRoot)

	http.HandleFunc("/v1/chat/completions", chatService.handleChatCompletions)
	http.HandleFunc("/v1/diff", chatService.handleDiff)
	http.HandleFunc("/health", handleHealth)

	// 添加CORS支持
	http.HandleFunc("/", handleCORS)

	// 从中心化的Git服务获取Git信息用于显示
	gitService := utils.GetGlobalGitService()
	gitInfo := gitService.GetGitInfo()

	address := "0.0.0.0:" + port
	fmt.Printf("🚀 CodeFuse CLI HTTP Server starting on %s\n", address)
	fmt.Printf("📁 Workspace: %s\n", workspaceRoot)
	fmt.Printf("📋 Repository Information:\n")
	fmt.Printf("  Git URL: %s\n", gitInfo.RemoteURL)
	fmt.Printf("  Branch: %s\n", gitInfo.Branch)
	fmt.Printf("  Commit: %s\n", gitInfo.CommitID)
	fmt.Println()

	// 打印可用工具信息
	chatService.printToolsInfo()

	// 确保远程索引存在（如果启用了远程检索功能）
	chatService.ensureRemoteIndexIfNeeded()

	fmt.Println("----------------------------------------")
	fmt.Printf("🌐 Server ready! Listening on http://%s\n", address)
	fmt.Println("📡 API Endpoints:")
	fmt.Println("  POST /v1/chat/completions - OpenAI compatible chat completions")
	fmt.Println("  GET  /v1/diff             - Get repository diff information")
	fmt.Println("  GET  /health              - Health check")
	fmt.Println()

	log.Fatal(http.ListenAndServe(address, nil))
}

// handleHealth 健康检查端点
func handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	json.NewEncoder(w).Encode(map[string]string{
		"status":    "ok",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleCORS 处理CORS预检请求
func handleCORS(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if r.Method == http.MethodOptions {
		w.WriteHeader(http.StatusOK)
		return
	}

	// 对于非OPTIONS请求，返回404
	http.NotFound(w, r)
}

// handleChatCompletions 处理聊天补全请求
func (hcs *HTTPChatService) handleChatCompletions(w http.ResponseWriter, r *http.Request) {
	// 记录请求接收日志
	log.Printf("🌐 Received request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)

	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	// 处理预检请求
	if r.Method == http.MethodOptions {
		w.WriteHeader(http.StatusOK)
		return
	}

	// 在请求处理时重新获取git信息，确保信息是最新的
	hcs.refreshGitInfo()

	// 尝试解析为OpenAI格式的请求
	var openaiReq OpenAIChatRequest
	var customReq ChatRequest
	var messages []openai.ChatCompletionMessage
	var taskID string
	var modelName string

	// 读取请求体
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	// 记录请求体内容（截断后）
	bodyPreview := string(body)
	if len(bodyPreview) > 200 {
		bodyPreview = bodyPreview[:200] + "..."
	}
	log.Printf("📄 Request body: %s", bodyPreview)

	// 首先尝试解析为OpenAI格式
	if err := json.Unmarshal(body, &openaiReq); err == nil && len(openaiReq.Messages) > 0 {
		// OpenAI格式请求
		messages = openaiReq.Messages
		taskID = fmt.Sprintf("openai-%d", time.Now().Unix())
		// 使用请求中的模型名，如果为空则使用默认配置
		if openaiReq.Model != "" {
			modelName = openaiReq.Model
		} else {
			modelName = hcs.config.AI.Model
		}
		log.Printf("📥 [%s] Received OpenAI format request with %d messages, model: %s", taskID, len(messages), modelName)
	} else {
		// 尝试解析为自定义格式
		if err := json.Unmarshal(body, &customReq); err != nil {
			http.Error(w, "Invalid JSON format", http.StatusBadRequest)
			return
		}

		if customReq.Query == "" {
			http.Error(w, "Query is required", http.StatusBadRequest)
			return
		}

		taskID = customReq.TaskID
		if taskID == "" {
			taskID = fmt.Sprintf("custom-%d", time.Now().Unix())
		}
		// 使用请求中的模型名，如果为空则使用默认配置
		if customReq.ModelName != "" {
			modelName = customReq.ModelName
		} else {
			modelName = hcs.config.AI.Model
		}
		log.Printf("📥 [%s] Received custom format request: %s, modelName: %s", taskID, customReq.Query, modelName)

		// 初始化系统 prompt
		systemPrompt, err := hcs.promptManager.GetSystemPrompt()
		if err != nil {
			log.Printf("Warning: Failed to get system prompt: %v", err)
			systemPrompt = "You are a helpful AI assistant."
		}

		supervisionPrompt, err := hcs.promptManager.GetSupervisionPrompt()
		if err != nil {
			log.Printf("Warning: Failed to get supervision prompt: %v", err)
			supervisionPrompt = ""
		}

		// 构建消息历史
		messages = append(messages, openai.ChatCompletionMessage{
			Role:    openai.ChatMessageRoleSystem,
			Content: systemPrompt,
		})

		// 如果有 supervision prompt，添加为用户消息
		if supervisionPrompt != "" {
			messages = append(messages, openai.ChatCompletionMessage{
				Role:    openai.ChatMessageRoleUser,
				Content: supervisionPrompt,
			})
		}

		// 添加用户消息
		messages = append(messages, openai.ChatCompletionMessage{
			Role:    openai.ChatMessageRoleUser,
			Content: customReq.Query,
		})
	}

	// 设置响应头
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")

	// 创建 flusher
	flusher, ok := w.(http.Flusher)
	if !ok {
		http.Error(w, "Streaming unsupported", http.StatusInternalServerError)
		return
	}

	// 生成唯一ID
	responseID := fmt.Sprintf("chatcmpl-%s-%d", taskID, time.Now().Unix())

	// 流式处理
	ctx := context.Background()
	if err := hcs.streamChatHTTP(ctx, messages, w, flusher, responseID, modelName); err != nil {
		log.Printf("❌ [%s] Error in stream chat: %v", responseID, err)
	} else {
		log.Printf("✅ [%s] Request completed successfully", responseID)
	}

	// 发送结束标记
	fmt.Fprintf(w, "data: [DONE]\n\n")
	flusher.Flush()
}

// streamChatHTTP HTTP模式的流式聊天
func (hcs *HTTPChatService) streamChatHTTP(ctx context.Context, messages []openai.ChatCompletionMessage, w http.ResponseWriter, flusher http.Flusher, responseID string, modelName string) error {
	req := openai.ChatCompletionRequest{
		Model:    modelName,
		Messages: messages,
		Tools:    hcs.toolManager.GetTools(),
		Stream:   true,
	}

	stream, err := hcs.client.CreateChatCompletionStream(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to create stream: %v", err)
	}
	defer stream.Close()

	var functionCalls []openai.ToolCall
	var assistantMessage strings.Builder

	// 本地日志：开始处理请求
	log.Printf("🤖 [%s] Codefuse正在处理请求...", responseID)

	// 发送处理开始消息
	hcs.sendStreamMessage(w, flusher, responseID, "", "assistant", nil, modelName)

	for {
		response, err := stream.Recv()
		if err != nil {
			if err == io.EOF {
				break
			}
			return fmt.Errorf("stream error: %v", err)
		}

		// 检查是否有usage信息需要转发
		if response.Usage.TotalTokens > 0 {
			usage := &Usage{
				CompletionTokens: response.Usage.CompletionTokens,
				PromptTokens:     response.Usage.PromptTokens,
				TotalTokens:      response.Usage.TotalTokens,
			}

			// 处理 CompletionTokensDetails
			if response.Usage.CompletionTokensDetails != nil {
				usage.CompletionTokensDetails = make(map[string]interface{})
				if response.Usage.CompletionTokensDetails.AudioTokens > 0 {
					usage.CompletionTokensDetails["audio_tokens"] = response.Usage.CompletionTokensDetails.AudioTokens
				}
				if response.Usage.CompletionTokensDetails.ReasoningTokens > 0 {
					usage.CompletionTokensDetails["reasoning_tokens"] = response.Usage.CompletionTokensDetails.ReasoningTokens
				}
			}

			// 处理 PromptTokensDetails，包括 cached_tokens
			if response.Usage.PromptTokensDetails != nil {
				usage.PromptTokensDetails = make(map[string]interface{})
				if response.Usage.PromptTokensDetails.AudioTokens > 0 {
					usage.PromptTokensDetails["audio_tokens"] = response.Usage.PromptTokensDetails.AudioTokens
				}
				if response.Usage.PromptTokensDetails.CachedTokens > 0 {
					usage.PromptTokensDetails["cached_tokens"] = response.Usage.PromptTokensDetails.CachedTokens
				}
			}

			// 发送包含usage信息的消息
			hcs.sendStreamMessageWithUsage(w, flusher, responseID, "", "", nil, modelName, usage)
		}

		if len(response.Choices) == 0 {
			continue
		}

		choice := response.Choices[0]
		delta := choice.Delta

		// 处理普通文本内容
		if delta.Content != "" {
			assistantMessage.WriteString(delta.Content)
			// 本地日志：打印模型输出（只打印，不换行，模拟流式输出）
			fmt.Print(delta.Content)
			hcs.sendStreamMessage(w, flusher, responseID, delta.Content, "", nil, modelName)
		}

		// 处理工具调用
		if len(delta.ToolCalls) > 0 {
			for _, toolCall := range delta.ToolCalls {
				// 确保 Index 不为空
				if toolCall.Index == nil {
					continue
				}

				// 查找或创建对应的工具调用
				for len(functionCalls) <= *toolCall.Index {
					functionCalls = append(functionCalls, openai.ToolCall{})
				}

				existingCall := &functionCalls[*toolCall.Index]

				// 设置基本信息
				if toolCall.ID != "" {
					existingCall.ID = toolCall.ID
					existingCall.Type = toolCall.Type
				}

				// 处理 Function 字段
				if toolCall.Function.Name != "" || toolCall.Function.Arguments != "" {
					if existingCall.Function.Name == "" {
						existingCall.Function.Name = toolCall.Function.Name
					}

					if toolCall.Function.Arguments != "" {
						existingCall.Function.Arguments += toolCall.Function.Arguments
					}
				}
			}

			// 发送工具调用增量
			toolCallDeltas := make([]ToolCall, len(delta.ToolCalls))
			for i, tc := range delta.ToolCalls {
				toolCallDeltas[i] = ToolCall{
					Index: tc.Index,
					ID:    tc.ID,
					Type:  string(tc.Type),
					Function: ToolFunction{
						Name:      tc.Function.Name,
						Arguments: tc.Function.Arguments,
					},
				}
			}
			hcs.sendStreamMessage(w, flusher, responseID, "", "", toolCallDeltas, modelName)
		}
	}

	// 本地日志：模型输出结束后换行
	if assistantMessage.Len() > 0 {
		fmt.Println() // 换行
	}

	// 如果有工具调用，执行它们
	if len(functionCalls) > 0 {
		// 添加助手消息（包含工具调用）
		assistantMsg := openai.ChatCompletionMessage{
			Role:      openai.ChatMessageRoleAssistant,
			Content:   assistantMessage.String(),
			ToolCalls: functionCalls,
		}
		messages = append(messages, assistantMsg)

		// 执行工具调用并添加结果
		for _, toolCall := range functionCalls {
			if toolCall.Function.Name != "" {
				// 本地日志：工具调用开始
				log.Printf("Calling_function: [%s]  %s with arguments: %s",
					responseID, toolCall.Function.Name, toolCall.Function.Arguments)

				// 发送工具执行开始信息
				//toolStartMsg := fmt.Sprintf("\n Calling_function: : %s with arguments: %s\n",
				//	toolCall.Function.Name, toolCall.Function.Arguments)
				//hcs.sendStreamMessage(w, flusher, responseID, toolStartMsg, "", nil)

				result, err := hcs.executeFunction(toolCall.Function.Name, toolCall.Function.Arguments)
				if err != nil {
					result = fmt.Sprintf("Error: %v", err)
				}

				// 本地日志：工具执行结果
				log.Printf("Funcation_Result: [%s] : %s", responseID, result)

				// 发送工具执行结果
				toolResultMsg := fmt.Sprintf("Funcation_Result: %s\n\n", result)
				hcs.sendStreamMessage(w, flusher, responseID, toolResultMsg, "", nil, modelName)

				// 添加工具调用结果消息
				toolMsg := openai.ChatCompletionMessage{
					Role:       openai.ChatMessageRoleTool,
					Content:    result,
					ToolCallID: toolCall.ID,
				}
				messages = append(messages, toolMsg)
			}
		}

		// 本地日志：开始最终回复
		log.Printf(" [%s] Codefuse正在生成最终回复...", responseID)
		fmt.Print("\nCodefuse: \n")

		// 发送最终处理信息
		hcs.sendStreamMessage(w, flusher, responseID, "\nCodefuse: \n", "", nil, modelName)

		// 再次调用模型获取最终回复
		return hcs.streamChatHTTP(ctx, messages, w, flusher, responseID, modelName)
	}

	return nil
}

// sendStreamMessage 发送流式消息
func (hcs *HTTPChatService) sendStreamMessage(w http.ResponseWriter, flusher http.Flusher, id, content, role string, toolCalls []ToolCall, modelName string) {
	hcs.sendStreamMessageWithUsage(w, flusher, id, content, role, toolCalls, modelName, nil)
}

// sendStreamMessageWithUsage 发送带usage信息的流式消息
func (hcs *HTTPChatService) sendStreamMessageWithUsage(w http.ResponseWriter, flusher http.Flusher, id, content, role string, toolCalls []ToolCall, modelName string, usage *Usage) {
	response := ChatStreamResponse{
		ID:      id,
		Object:  "chat.completion.chunk",
		Created: time.Now().Unix(),
		Model:   modelName,
		Choices: []ChatStreamChoice{
			{
				Index: 0,
				Delta: ChatStreamDelta{
					Role:      role,
					Content:   content,
					ToolCalls: toolCalls,
				},
			},
		},
		Usage: usage,
	}

	data, err := json.Marshal(response)
	if err != nil {
		log.Printf("Error marshaling response: %v", err)
		return
	}

	fmt.Fprintf(w, "data: %s\n\n", data)
	flusher.Flush()
}

// handleDiff 处理获取仓库diff信息的请求
func (hcs *HTTPChatService) handleDiff(w http.ResponseWriter, r *http.Request) {
	// 记录请求接收日志
	log.Printf("🌐 Received diff request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)

	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
	w.Header().Set("Content-Type", "application/json")

	// 处理预检请求
	if r.Method == http.MethodOptions {
		w.WriteHeader(http.StatusOK)
		return
	}

	// 只允许GET请求
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 在请求处理时重新获取git信息，确保信息是最新的
	hcs.refreshGitInfo()

	// 获取diff信息
	diffInfo, err := utils.GetDiffInfo()
	if err != nil {
		log.Printf("❌ Error getting diff info: %v", err)
		http.Error(w, "Failed to get diff information", http.StatusInternalServerError)
		return
	}

	// 返回JSON响应
	if err := json.NewEncoder(w).Encode(diffInfo); err != nil {
		log.Printf("❌ Error encoding diff response: %v", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}

	log.Printf("✅ Diff request completed successfully with status: %s, %d files",
		diffInfo.DiffStatus, len(diffInfo.FileDetails))
}
