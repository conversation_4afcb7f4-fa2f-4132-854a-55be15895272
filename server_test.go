package main

import (
	"encoding/json"
	"testing"

	"github.com/sashabaranov/go-openai"
)

// TestUsageHandling 测试 Usage 信息的处理
func TestUsageHandling(t *testing.T) {
	// 模拟从 go-openai 包接收到的 Usage 信息
	openaiUsage := openai.Usage{
		PromptTokens:     1672,
		CompletionTokens: 217,
		TotalTokens:      1889,
		PromptTokensDetails: &openai.PromptTokensDetails{
			AudioTokens:  0,
			CachedTokens: 3, // 这是我们要测试的关键字段
		},
		CompletionTokensDetails: &openai.CompletionTokensDetails{
			AudioTokens:     0,
			ReasoningTokens: 0,
		},
	}

	// 创建我们的 Usage 结构体
	usage := &Usage{
		CompletionTokens: openaiUsage.CompletionTokens,
		PromptTokens:     openaiUsage.PromptTokens,
		TotalTokens:      openaiUsage.TotalTokens,
	}

	// 处理 CompletionTokensDetails
	if openaiUsage.CompletionTokensDetails != nil {
		usage.CompletionTokensDetails = make(map[string]interface{})
		if openaiUsage.CompletionTokensDetails.AudioTokens > 0 {
			usage.CompletionTokensDetails["audio_tokens"] = openaiUsage.CompletionTokensDetails.AudioTokens
		}
		if openaiUsage.CompletionTokensDetails.ReasoningTokens > 0 {
			usage.CompletionTokensDetails["reasoning_tokens"] = openaiUsage.CompletionTokensDetails.ReasoningTokens
		}
	}

	// 处理 PromptTokensDetails，包括 cached_tokens
	if openaiUsage.PromptTokensDetails != nil {
		usage.PromptTokensDetails = make(map[string]interface{})
		if openaiUsage.PromptTokensDetails.AudioTokens > 0 {
			usage.PromptTokensDetails["audio_tokens"] = openaiUsage.PromptTokensDetails.AudioTokens
		}
		if openaiUsage.PromptTokensDetails.CachedTokens > 0 {
			usage.PromptTokensDetails["cached_tokens"] = openaiUsage.PromptTokensDetails.CachedTokens
		}
	}

	// 验证基本字段
	if usage.PromptTokens != 1672 {
		t.Errorf("Expected PromptTokens to be 1672, got %d", usage.PromptTokens)
	}
	if usage.CompletionTokens != 217 {
		t.Errorf("Expected CompletionTokens to be 217, got %d", usage.CompletionTokens)
	}
	if usage.TotalTokens != 1889 {
		t.Errorf("Expected TotalTokens to be 1889, got %d", usage.TotalTokens)
	}

	// 验证 cached_tokens 字段
	if usage.PromptTokensDetails == nil {
		t.Fatal("PromptTokensDetails should not be nil")
	}
	cachedTokens, exists := usage.PromptTokensDetails["cached_tokens"]
	if !exists {
		t.Fatal("cached_tokens should exist in PromptTokensDetails")
	}
	if cachedTokens != 3 {
		t.Errorf("Expected cached_tokens to be 3, got %v", cachedTokens)
	}

	// 测试 JSON 序列化
	jsonData, err := json.Marshal(usage)
	if err != nil {
		t.Fatalf("Failed to marshal usage to JSON: %v", err)
	}

	// 验证 JSON 输出包含 cached_tokens
	var result map[string]interface{}
	err = json.Unmarshal(jsonData, &result)
	if err != nil {
		t.Fatalf("Failed to unmarshal JSON: %v", err)
	}

	promptTokensDetails, ok := result["prompt_tokens_details"].(map[string]interface{})
	if !ok {
		t.Fatal("prompt_tokens_details should be a map")
	}

	cachedTokensFromJSON, exists := promptTokensDetails["cached_tokens"]
	if !exists {
		t.Fatal("cached_tokens should exist in JSON output")
	}

	// JSON 数字会被解析为 float64
	if cachedTokensFromJSON != float64(3) {
		t.Errorf("Expected cached_tokens in JSON to be 3, got %v", cachedTokensFromJSON)
	}

	t.Logf("JSON output: %s", string(jsonData))
}

// TestChatStreamResponse 测试完整的 ChatStreamResponse 结构
func TestChatStreamResponse(t *testing.T) {
	usage := &Usage{
		CompletionTokens: 217,
		PromptTokens:     1672,
		TotalTokens:      1889,
		PromptTokensDetails: map[string]interface{}{
			"cached_tokens": 3,
		},
	}

	response := ChatStreamResponse{
		ID:      "chatcmpl-test-123",
		Object:  "chat.completion.chunk",
		Created: 1753347144,
		Model:   "Kimi-K2-Instruct",
		Choices: []ChatStreamChoice{
			{
				Index: 0,
				Delta: ChatStreamDelta{},
			},
		},
		Usage: usage,
	}

	// 测试 JSON 序列化
	jsonData, err := json.Marshal(response)
	if err != nil {
		t.Fatalf("Failed to marshal response to JSON: %v", err)
	}

	// 验证 JSON 输出
	var result map[string]interface{}
	err = json.Unmarshal(jsonData, &result)
	if err != nil {
		t.Fatalf("Failed to unmarshal JSON: %v", err)
	}

	// 检查 usage 字段
	usageFromJSON, ok := result["usage"].(map[string]interface{})
	if !ok {
		t.Fatal("usage should be a map")
	}

	// 检查 prompt_tokens_details
	promptTokensDetails, ok := usageFromJSON["prompt_tokens_details"].(map[string]interface{})
	if !ok {
		t.Fatal("prompt_tokens_details should be a map")
	}

	// 检查 cached_tokens
	cachedTokens, exists := promptTokensDetails["cached_tokens"]
	if !exists {
		t.Fatal("cached_tokens should exist in JSON output")
	}

	if cachedTokens != float64(3) {
		t.Errorf("Expected cached_tokens to be 3, got %v", cachedTokens)
	}

	t.Logf("Complete response JSON: %s", string(jsonData))
}
